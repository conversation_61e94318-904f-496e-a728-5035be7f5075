===============================================================================
                    DI SORUNLARI ÇÖZÜM PLANI - DETAYLI PROMPT LİSTESİ
                    ENTERPRISE MULTI-TENANT SaaS İÇİN OPTİMİZE EDİLMİŞ
===============================================================================

🎯 PROJE HEDEFİ VE VİZYON
=========================
Bu proje, Türkiye'nin en büyük spor salonu yönetim SaaS platformu olmayı hedefliyor.

📈 BÜYÜK HEDEF:
- 🏢 1000+ spor salonuna sistem satışı
- 👥 100.000+ aktif kull<PERSON> (salon müşterileri)
- 🗄️ Tek veritabanı üzerinden multi-tenant mimari
- 🚀 Enterprise-grade performans ve güvenilirlik
- 💰 Türkiye spor salonu pazarında lider platform

🏗️ MEVCUT PROJE YAPISI
=======================
📁 GymProjectBackend (ASP.NET Core Web API)
├── 📁 Core (Temel altyapı katmanı)
│   ├── DataAccess/ (Repository pattern, Entity Framework)
│   ├── Utilities/ (JWT, Security, Caching, Paging)
│   └── Entities/ (Base entity interfaces)
├── 📁 Entities (Veri modelleri)
│   ├── Concrete/ (Member, Payment, Membership, Company vb.)
│   └── DTOs/ (Data Transfer Objects)
├── 📁 DataAccess (Veri erişim katmanı)
│   ├── Abstract/ (Repository interfaces - 31 adet)
│   └── Concrete/EntityFramework/ (EF implementations - 31 adet)
├── 📁 Business (İş mantığı katmanı)
│   ├── Abstract/ (Service interfaces)
│   ├── Concrete/ (Manager sınıfları)
│   └── DependencyResolvers/Autofac/ (DI configuration)
└── 📁 WebAPI (API endpoints)

📁 GymProjectFrontend (Angular 17)
├── 📁 src/app/
│   ├── components/ (UI bileşenleri)
│   ├── services/ (API servisleri)
│   ├── models/ (TypeScript modelleri)
│   └── guards/ (Route guards, authentication)

🏢 MULTI-TENANT MİMARİ
======================
- 🔐 Her salon ayrı CompanyID ile izole
- 👤 Kullanıcılar JWT token ile authenticate
- 🏪 Salon sahipleri kendi verilerini yönetir
- 📊 Merkezi dashboard ve raporlama
- 💳 Salon bazlı ödeme ve üyelik yönetimi

💼 İŞ MODELİ
============
- 💰 Salon başına aylık abonelik ücreti
- 📈 Kullanıcı sayısına göre ölçeklenebilir fiyatlandırma
- 🎯 Hedef: 1000 salon × 500₺/ay = 500.000₺/ay gelir
- 🚀 Büyüme hedefi: 3 yıl içinde Türkiye'nin %30'u

🔧 MEVCUT TEKNİK STACK
======================
Backend:
- ⚙️ ASP.NET Core 6.0 Web API
- 🗄️ Entity Framework Core 6.0
- 🏗️ Autofac DI Container
- 🔐 JWT Authentication
- 📊 SQL Server Database
- 🎯 Repository + Unit of Work Pattern

Frontend:
- 🅰️ Angular 17
- 🎨 Bootstrap + Custom CSS
- 📱 Responsive design
- 🔄 RxJS for reactive programming

🚨 MEVCUT KRİTİK SORUNLAR
=========================
1. 💥 DbContext DI lifetime uyumsuzluğu
2. 🔴 Memory leak riski (100.000 kullanıcıda sistem çöker)
3. ⚠️ Connection pool tükenmesi
4. 🐌 Performans sorunları (response time >2000ms)
5. 🔓 Multi-tenant güvenlik açıkları
6. 📈 Ölçeklenebilirlik problemleri

🎯 ÇÖZÜM SONRASI HEDEFLER
=========================
- ⚡ Response time: <200ms
- 🏃‍♂️ 100.000+ eşzamanlı kullanıcı desteği
- 💾 Memory usage: %80 azalma
- 🔒 %100 multi-tenant güvenlik
- 📊 7/24 uptime garantisi
- 🚀 1000+ salon ready infrastructure

===============================================================================

📊 GENEL DURUM ANALİZİ
======================
- Toplam DAL Dosyası: 31 adet
- DbContext DI Kaydı: ❌ YOK (KRİTİK SORUN)
- DAL Lifetime: ❌ InstancePerDependency (Memory Leak)
- DbContext Lifetime: ❌ InstancePerLifetimeScope (Uyumsuzluk)
- Memory Leak Riski: 🔴 KRİTİK (100.000 kullanıcıda sistem çöker)
- Connection Pool: 🔴 KRİTİK (Tükeniyor)
- Multi-Tenant Güvenlik: ⚠️ RİSKLİ
- Toplam Kod Satırı: 4,500+ satır

🔥 KRİTİK ENTERPRISE SORUNLAR
=============================
1. DbContext her metotta yeniden yaratılıyor (new TContext())
2. DI'da DbContext scoped ama DAL'larda kullanılmıyor
3. DAL'lar transient ama DbContext scoped (Lifetime uyumsuzluğu)
4. 100.000 kullanıcıda saniyede 1M+ DbContext instance'ı
5. Connection pool tükenmesi garantili
6. Transaction tutarsızlığı (her metod ayrı transaction)
7. Multi-tenant data leak riski

📋 DAL DOSYALARININ ENTERPRISE YÜK ANALİZİ
==========================================

🔥 MEGA BOYUTLU DAL'LAR (500+ satır) - ENTERPRISE KRİTİK:
- EfMemberDal.cs: 804 satır ⚠️ MEGA! (En yoğun kullanım)
- EfPaymentDal.cs: 488 satır ⚠️ MEGA! (Mali işlemler)

🔴 BÜYÜK DAL'LAR (300-500 satır) - YÜKSEK PRİORİTE:
- EfCompanyUserDal.cs: 407 satır ⚠️ BÜYÜK!
- EfCompanyExerciseDal.cs: 344 satır ⚠️ BÜYÜK!
- EfMemberWorkoutProgramDal.cs: 320 satır ⚠️ BÜYÜK!

🟡 ORTA BOYUTLU DAL'LAR (100-300 satır) - ORTA PRİORİTE:
- EfUserLicenseDal.cs: 250 satır
- EfMembershipDal.cs: 223 satır
- EfSystemExerciseDal.cs: 188 satır
- EfWorkoutProgramTemplateDal.cs: 149 satır
- EfUserDal.cs: 138 satır
- EfExpenseDal.cs: 136 satır
- EfMembershipTypeDal.cs: 101 satır

🟢 KÜÇÜK DAL'LAR (50-100 satır) - DÜŞÜK PRİORİTE:
- EfMembershipFreezeHistoryDal.cs: 97 satır
- EfProductDal.cs: 78 satır
- EfTransactionDal.cs: 59 satır
- EfDebtPaymentDal.cs: 55 satır

📝 MİNİ DAL'LAR (50 satır altı) - TOPLU İŞLEM:
- EfRemainingDebtDal.cs: 49 satır
- EfUserCompanyDal.cs: 48 satır
- EfExerciseCategoryDal.cs: 49 satır
- EfCompanyAdressDal.cs: 36 satır
- EfUserOperationClaimDal.cs: 35 satır
- EfCompanyDal.cs: 32 satır
- EfUserDeviceDal.cs: 31 satır
- EfEntryExitHistoryDal.cs: 19 satır
- EfCityDal.cs: 14 satır
- EfTownDal.cs: 14 satır
- EfLicensePackageDal.cs: 14 satır
- EfOperationClaimDal.cs: 14 satır
- EfWorkoutProgramDayDal.cs: 13 satır
- EfWorkoutProgramExerciseDal.cs: 13 satır
- EfLicenseTransactionDal.cs: 9 satır

🎯 ENTERPRISE-GRADE GÜNCELLEME STRATEJİSİ
=========================================

TOPLAM: 10 PROMPT (Enterprise Performance Odaklı)

PROMPT 1: ENTERPRISE FOUNDATION - BASE REPOSITORY'LER
=====================================================
Dosyalar:
- Core/DataAccess/EntityFramework/EfEntityRepositoryBase.cs
- Core/DataAccess/EntityFramework/EfCompanyEntityRepositoryBase.cs

Enterprise Yapılacaklar:
✅ DbContext constructor injection pattern
✅ Async-first operations (100.000 kullanıcı için zorunlu)
✅ Connection pooling awareness
✅ Query optimization hints
✅ Memory leak prevention
✅ using (new TContext()) → _context kullanımına çevir
✅ Enterprise error handling
✅ Performance monitoring hooks

🔧 MANUEL DbContext DÜZELTME TALİMATLARI:
❌ BULUP SİL: using (TContext context = new TContext())
❌ BULUP SİL: using (var context = new TContext())
❌ BULUP SİL: new TContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ EKLE: Constructor injection pattern
✅ EKLE: Async/await operations

Beklenen Sonuç: Foundation hazır, memory leak %90 azalma

PROMPT 2: MİNİ DAL'LAR TOPLU ENTERPRISE UPGRADE (8 dosya)
=========================================================
Dosyalar:
- EfCityDal.cs (14 satır)
- EfTownDal.cs (14 satır) 
- EfLicensePackageDal.cs (14 satır)
- EfOperationClaimDal.cs (14 satır)
- EfWorkoutProgramDayDal.cs (13 satır)
- EfWorkoutProgramExerciseDal.cs (13 satır)
- EfLicenseTransactionDal.cs (9 satır)
- EfEntryExitHistoryDal.cs (19 satır)

Enterprise Yapılacaklar:
✅ Constructor injection pattern
✅ Async operations ekle
✅ Enterprise error handling
✅ Multi-tenant filtering (gerekirse)
✅ Performance optimization

🔧 MANUEL DbContext DÜZELTME TALİMATLARI:
❌ BULUP SİL: using (GymContext context = new GymContext())
❌ BULUP SİL: using (var context = new GymContext())
❌ BULUP SİL: new GymContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ EKLE: public EfXxxDal(GymContext context) : base(context) { }
✅ EKLE: Async metodlar (GetAllAsync, GetAsync, AddAsync vb.)

Risk: ✅ DÜŞÜK (Basit DAL'lar)
Test: Basic CRUD operations

PROMPT 3: KÜÇÜK DAL'LAR ENTERPRISE UPGRADE (7 dosya)
====================================================
Dosyalar:
- EfUserDeviceDal.cs (31 satır)
- EfCompanyDal.cs (32 satır)
- EfCompanyAdressDal.cs (36 satır)
- EfUserOperationClaimDal.cs (35 satır)
- EfUserCompanyDal.cs (48 satır)
- EfExerciseCategoryDal.cs (49 satır)
- EfRemainingDebtDal.cs (49 satır)

Enterprise Yapılacaklar:
✅ Constructor injection pattern
✅ Multi-tenant security (Company DAL'lar için)
✅ Async operations
✅ Bulk operations support
✅ Caching hints

🔧 MANUEL DbContext DÜZELTME TALİMATLARI:
❌ BULUP SİL: using (GymContext context = new GymContext())
❌ BULUP SİL: using (var context = new GymContext())
❌ BULUP SİL: new GymContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ EKLE: Constructor injection (özellikle Company DAL'lar için)
✅ EKLE: ICompanyContext parameter (Company DAL'lar için)

Risk: ✅ DÜŞÜK
Test: Multi-tenant isolation

PROMPT 4: ORTA DAL'LAR GRUP A - ENTERPRISE PERFORMANCE (4 dosya)
===============================================================
Dosyalar:
- EfTransactionDal.cs (59 satır)
- EfDebtPaymentDal.cs (55 satır)
- EfProductDal.cs (78 satır)
- EfMembershipFreezeHistoryDal.cs (97 satır)

Enterprise Yapılacaklar:
✅ Constructor injection düzelt (zaten var olanları)
✅ using (new GymContext()) → _context kullanımına çevir
✅ Async operations upgrade
✅ Transaction optimization
✅ Financial data security (Transaction, Debt için)
✅ Audit logging hooks

🔧 MANUEL DbContext DÜZELTME TALİMATLARI:
❌ BULUP SİL: using (var context = new GymContext())
❌ BULUP SİL: using (GymContext context = new GymContext())
❌ BULUP SİL: new GymContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ DÜZELTİLMİŞ CONSTRUCTOR: Mevcut constructor'ları kontrol et
✅ ASYNC UPGRADE: Sync metodları async'e çevir

Risk: ⚠️ ORTA (Financial operations)
Test: Transaction consistency

PROMPT 5: ORTA DAL'LAR GRUP B - ENTERPRISE SCALE (4 dosya)
==========================================================
Dosyalar:
- EfMembershipTypeDal.cs (101 satır)
- EfUserDal.cs (138 satır)
- EfExpenseDal.cs (136 satır)
- EfWorkoutProgramTemplateDal.cs (149 satır)

Enterprise Yapılacaklar:
✅ Constructor injection ekle/düzelt
✅ using (new GymContext()) → _context kullanımına çevir
✅ High-performance queries
✅ Pagination optimization
✅ Caching integration points
✅ Bulk operations

🔧 MANUEL DbContext DÜZELTME TALİMATLARI:
❌ BULUP SİL: using (GymContext context = new GymContext())
❌ BULUP SİL: using (var context = new GymContext())
❌ BULUP SİL: new GymContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ CONSTRUCTOR EKLE/DÜZELTİ: DbContext injection pattern
✅ PERFORMANCE: Complex query'leri optimize et

Risk: ⚠️ ORTA
Test: Performance benchmarks

PROMPT 6: ENTERPRISE CRITICAL - EfMemberDal (804 satır)
=======================================================
Dosyalar:
- EfMemberDal.cs (804 satır) - EN KRİTİK DAL

Enterprise Yapılacaklar:
✅ Constructor injection düzelt
✅ Tüm using (new GymContext()) → _context kullanımına çevir
✅ 15+ metodu async'e çevir
✅ Complex query optimization
✅ Pagination performance boost
✅ Member filtering optimization
✅ QR code operations optimization
✅ Birthday query caching
✅ Entry/exit performance critical
✅ Memory usage optimization

🔧 MANUEL DbContext DÜZELTME TALİMATLARI (KRİTİK - 804 SATIR):
❌ BULUP SİL: using (GymContext context = new GymContext()) - 15+ yerde var!
❌ BULUP SİL: using (var context = new GymContext()) - 10+ yerde var!
❌ BULUP SİL: new GymContext() - Her metotta var!
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ CONSTRUCTOR DÜZELTİ: Mevcut constructor'ı optimize et
✅ ASYNC DÖNÜŞÜM: 15+ metodu tek tek async'e çevir
✅ COMPLEX QUERY: Join'li sorguları optimize et

Risk: 🔴 YÜKSEK (En kritik DAL)
Test: Load testing (1000 concurrent users)
Beklenen: Response time <200ms

PROMPT 7: ENTERPRISE FINANCIAL - EfPaymentDal (488 satır)
=========================================================
Dosyalar:
- EfPaymentDal.cs (488 satır) - MALİ İŞLEMLER

Enterprise Yapılacaklar:
✅ Constructor injection düzelt
✅ Tüm using (new GymContext()) → _context kullanımına çevir
✅ Financial query optimization
✅ Revenue calculation performance
✅ Payment history pagination
✅ Debt management optimization
✅ Financial security enhancements
✅ Audit trail integration

🔧 MANUEL DbContext DÜZELTME TALİMATLARI (MALİ İŞLEMLER - 488 SATIR):
❌ BULUP SİL: using (var context = new GymContext()) - 8+ yerde var!
❌ BULUP SİL: using (GymContext context = new GymContext()) - 5+ yerde var!
❌ BULUP SİL: new GymContext() - Financial metodlarda var!
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ CONSTRUCTOR DÜZELTİ: Mevcut constructor'ı kontrol et
✅ FINANCIAL ASYNC: Payment metodlarını async'e çevir
✅ SECURITY: Financial data için extra güvenlik

Risk: 🔴 YÜKSEK (Financial operations)
Test: Financial data integrity
Beklenen: Transaction consistency %100

PROMPT 8: BÜYÜK DAL'LAR ENTERPRISE UPGRADE (3 dosya)
====================================================
Dosyalar:
- EfSystemExerciseDal.cs (188 satır)
- EfMembershipDal.cs (223 satır)
- EfUserLicenseDal.cs (250 satır)

Enterprise Yapılacaklar:
✅ Constructor injection ekle/düzelt
✅ using (new GymContext()) → _context kullanımına çevir
✅ Complex business logic optimization
✅ License management performance
✅ Membership operations scaling
✅ Exercise system optimization

🔧 MANUEL DbContext DÜZELTME TALİMATLARI (BÜYÜK DAL'LAR):
❌ BULUP SİL: using (GymContext context = new GymContext())
❌ BULUP SİL: using (var context = new GymContext())
❌ BULUP SİL: new GymContext() kullanımları
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ CONSTRUCTOR: Her DAL için injection pattern ekle
✅ BUSINESS LOGIC: Complex metodları async'e çevir

Risk: ⚠️ ORTA-YÜKSEK
Test: Business logic integrity

PROMPT 9: MEGA DAL'LAR FINAL ENTERPRISE (3 dosya)
=================================================
Dosyalar:
- EfCompanyUserDal.cs (407 satır)
- EfCompanyExerciseDal.cs (344 satır)
- EfMemberWorkoutProgramDal.cs (320 satır)

Enterprise Yapılacaklar:
✅ Her birini enterprise pattern'e çevir
✅ Constructor injection ekle/düzelt
✅ using (new GymContext()) → _context kullanımına çevir
✅ Complex join optimization
✅ Workout system performance
✅ Company operations scaling

🔧 MANUEL DbContext DÜZELTME TALİMATLARI (MEGA DAL'LAR):
❌ BULUP SİL: using (GymContext context = new GymContext()) - Çok sayıda!
❌ BULUP SİL: using (var context = new GymContext()) - Her dosyada var!
❌ BULUP SİL: new GymContext() - Complex metodlarda var!
✅ DEĞİŞTİR: Tüm manuel context → _context kullanımına çevir
✅ MEGA CONSTRUCTOR: Her mega DAL için injection
✅ COMPLEX ASYNC: Join'li sorguları async'e çevir

Risk: 🔴 YÜKSEK (Complex operations)
Test: Complex query performance

PROMPT 10: ENTERPRISE DI CONFIGURATION & FINAL OPTIMIZATION
===========================================================
Dosyalar:
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs
- WebAPI/Program.cs (opsiyonel)

Enterprise Yapılacaklar:
✅ DbContext enterprise registration
✅ Connection pool optimization (1000 salon için)
✅ DAL lifetime optimization (InstancePerLifetimeScope)
✅ Memory management configuration
✅ Performance monitoring integration
✅ Health checks implementation
✅ Final enterprise validation
✅ Load testing preparation

Risk: ⚠️ ORTA (Configuration)
Test: Full system integration
Beklenen: 100.000 kullanıcı ready

⏱️ ENTERPRISE SÜRE TAHMİNİ
===========================
- Prompt 1-5: 10-15 dakika/prompt (Foundation)
- Prompt 6-7: 20-30 dakika/prompt (Critical DAL'lar)
- Prompt 8-9: 15-20 dakika/prompt (Complex DAL'lar)
- Prompt 10: 15-20 dakika (Final configuration)
- Toplam: 2.5-3.5 saat
- Test süreleri: +1 saat
- **TOPLAM: 3.5-4.5 saat**

🔧 ENTERPRISE DEĞİŞİKLİKLER ÖZETİ
=================================

1. ENTERPRISE CONSTRUCTOR INJECTION PATTERN:
   ÖNCE: public class EfUserDal : EfEntityRepositoryBase<User, GymContext>
   SONRA: public class EfUserDal : EfEntityRepositoryBase<User, GymContext>
          {
              public EfUserDal(GymContext context) : base(context) { }
          }

2. ENTERPRISE CONTEXT KULLANIMI:
   ÖNCE: using (GymContext context = new GymContext()) // ❌ Memory Leak
   SONRA: // _context kullan (base class'tan gelecek) // ✅ Enterprise

3. ENTERPRISE DI KAYITLARI:
   ÖNCE: builder.RegisterType<EfUserDal>().As<IUserDal>().InstancePerDependency(); // ❌
   SONRA: builder.RegisterType<EfUserDal>().As<IUserDal>().InstancePerLifetimeScope(); // ✅

4. ENTERPRISE DBCONTEXT KAYDI:
   EKLE: builder.Register(c => {
             var optionsBuilder = new DbContextOptionsBuilder<GymContext>();
             optionsBuilder.UseSqlServer(connectionString, options => {
                 options.CommandTimeout(30);
                 options.EnableRetryOnFailure(3);
             });
             return new GymContext(optionsBuilder.Options);
         }).InstancePerLifetimeScope();

💡 ENTERPRISE FAYDALAR
======================
- ✅ Memory leak'ler %100 önlenir
- ✅ 100.000+ kullanıcı performansı
- ✅ Connection pooling optimize
- ✅ Response time <200ms
- ✅ Multi-tenant güvenlik %100
- ✅ Transaction management enterprise-grade
- ✅ Scalability 1000+ salon
- ✅ Memory usage %80 azalma
- ✅ Database connection efficiency %90 artış
- ✅ System stability 7/24 uptime

===============================================================================
                              PROMPT KULLANIM KILAVUZU
===============================================================================

Kullanım:
1. "PROMPT 1 den başla" 
2. Her promptu bitirdiğinde projeyi build'le
3. Enterprise test senaryolarını çalıştır
4. buildde sorun çıkmazsa sonraki prompta geç.
5. Sorun çıkarsa hatayı çöz ve tekrar build/test et.

Her prompt enterprise-grade test edilebilir ve production-ready.
